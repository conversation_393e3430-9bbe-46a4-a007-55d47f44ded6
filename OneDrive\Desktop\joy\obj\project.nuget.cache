{"version": 2, "dgSpecHash": "FL7AGL95yQE=", "success": false, "projectFilePath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\joy\\KeyboardMouseEmulator.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\8.0.0\\system.codedom.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\8.0.0\\system.management.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.ref\\8.0.17\\microsoft.netcore.app.ref.8.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.ref\\8.0.17\\microsoft.windowsdesktop.app.ref.8.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.ref\\8.0.17\\microsoft.aspnetcore.app.ref.8.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.host.win-x64\\8.0.17\\microsoft.netcore.app.host.win-x64.8.0.17.nupkg.sha512"], "logs": [{"code": "NU1102", "level": "Error", "message": "Unable to find package Microsoft.Toolkit.Win32.UI.Controls with version (>= 6.1.3)\r\n  - Found 9 version(s) in nuget.org [ Nearest version: 5.0.0 ]", "libraryId": "Microsoft.Toolkit.Win32.UI.Controls", "targetGraphs": ["net8.0-windows7.0"]}]}