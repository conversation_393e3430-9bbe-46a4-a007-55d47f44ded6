using System.Windows;

namespace KeyboardMouseEmulator
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            // Initialize application-wide services
            ServiceLocator.Initialize();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            // Cleanup resources
            ServiceLocator.Cleanup();
            base.OnExit(e);
        }
    }
}
