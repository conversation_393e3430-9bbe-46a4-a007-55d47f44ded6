using KeyboardMouseEmulator.Services;
using System;
using System.Collections.Generic;

namespace KeyboardMouseEmulator
{
    public static class ServiceLocator
    {
        private static readonly Dictionary<Type, object> _services = new();
        private static bool _isInitialized = false;

        public static void Initialize()
        {
            if (_isInitialized) return;

            // Register core services
            RegisterService<IInputSimulationService>(new InputSimulationService());
            RegisterService<IScriptEngineService>(new ScriptEngineService());
            RegisterService<ISettingsService>(new SettingsService());
            RegisterService<IHotkeyService>(new HotkeyService());

            _isInitialized = true;
        }

        public static void RegisterService<T>(T service) where T : class
        {
            _services[typeof(T)] = service;
        }

        public static T GetService<T>() where T : class
        {
            if (_services.TryGetValue(typeof(T), out var service))
            {
                return (T)service;
            }
            throw new InvalidOperationException($"Service of type {typeof(T).Name} is not registered.");
        }

        public static void Cleanup()
        {
            foreach (var service in _services.Values)
            {
                if (service is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
            _services.Clear();
            _isInitialized = false;
        }
    }
}
